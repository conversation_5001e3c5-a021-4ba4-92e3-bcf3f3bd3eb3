import React, { useState, useEffect } from 'react';

interface TodoItem {
  id: number;
  text: string;
  completed: boolean;
}

interface TodoWidgetProps {
  widgetId?: string;
  enablePersistence?: boolean;
}

export const TodoWidget: React.FC<TodoWidgetProps> = ({
  widgetId = 'todo',
  enablePersistence = true,
}) => {
  // Simple state management
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [inputValue, setInputValue] = useState('');

  // Load from localStorage on mount if persistence is enabled
  useEffect(() => {
    if (enablePersistence && widgetId) {
      const saved = localStorage.getItem(`widget-${widgetId}`);
      if (saved) {
        try {
          const data = JSON.parse(saved);
          setTodos(data.todos || []);
          setInputValue(data.inputValue || '');
        } catch (error) {
          console.warn('Failed to load widget state:', error);
        }
      }
    }
  }, [widgetId, enablePersistence]);

  // Save to localStorage when state changes (if persistence enabled)
  useEffect(() => {
    if (enablePersistence && widgetId) {
      localStorage.setItem(`widget-${widgetId}`, JSON.stringify({ todos, inputValue }));
    }
  }, [todos, inputValue, widgetId, enablePersistence]);

  const addTodo = () => {
    if (inputValue.trim()) {
      const newTodos = [...todos, {
        id: Date.now(),
        text: inputValue.trim(),
        completed: false
      }];
      setTodos(newTodos);
      setInputValue('');
    }
  };

  const toggleTodo = (id: number) => {
    const newTodos = todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    );
    setTodos(newTodos);
  };

  const deleteTodo = (id: number) => {
    const newTodos = todos.filter(todo => todo.id !== id);
    setTodos(newTodos);
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
  };

  return (
    <div style={{
      padding: '20px',
      borderRadius: '8px',
      backgroundColor: '#f8f9fa',
      minHeight: '300px'
    }}>
      <h3>Todo Widget</h3>
      <div style={{ margin: '10px 0' }}>
        <input
          type="text"
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && addTodo()}
          placeholder="Add a new todo..."
          style={{
            padding: '8px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            marginRight: '10px',
            width: '200px'
          }}
        />
        <button
          onClick={addTodo}
          style={{
            padding: '8px 16px',
            backgroundColor: '#ffc107',
            color: 'black',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Add
        </button>
      </div>
      <div>
        {todos.map(todo => (
          <div key={todo.id} style={{
            display: 'flex',
            alignItems: 'center',
            margin: '5px 0',
            padding: '5px',
            backgroundColor: todo.completed ? '#e9ecef' : 'white',
            borderRadius: '4px'
          }}>
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
              style={{ marginRight: '10px' }}
            />
            <span style={{
              flex: 1,
              textDecoration: todo.completed ? 'line-through' : 'none',
              color: todo.completed ? '#6c757d' : 'black'
            }}>
              {todo.text}
            </span>
            <button
              onClick={() => deleteTodo(todo.id)}
              style={{
                padding: '4px 8px',
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Delete
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
