import React, { useState, useEffect } from 'react';

interface GreetingWidgetProps {
  defaultName?: string;
  widgetId?: string;
  enablePersistence?: boolean;
}

export const GreetingWidget: React.FC<GreetingWidgetProps> = ({
  defaultName = 'World',
  widgetId = 'greeting',
  enablePersistence = true,
}) => {
  // Simple state management
  const [name, setName] = useState(defaultName);

  // Load from localStorage on mount if persistence is enabled
  useEffect(() => {
    if (enablePersistence && widgetId) {
      const saved = localStorage.getItem(`widget-${widgetId}`);
      if (saved) {
        try {
          const data = JSON.parse(saved);
          setName(data.name || defaultName);
        } catch (error) {
          console.warn('Failed to load widget state:', error);
        }
      }
    }
  }, [widgetId, enablePersistence, defaultName]);

  // Save to localStorage when name changes (if persistence enabled)
  useEffect(() => {
    if (enablePersistence && widgetId) {
      localStorage.setItem(`widget-${widgetId}`, JSON.stringify({ name }));
    }
  }, [name, widgetId, enablePersistence]);

  const updateName = (newName: string) => {
    setName(newName);
  };

  return (
    <div style={{
      padding: '20px',
      borderRadius: '8px',
      backgroundColor: '#f8f9fa'
    }}>
      <h3>Greeting Widget</h3>
      <div style={{ margin: '10px 0' }}>
        <input
          type="text"
          value={name}
          onChange={(e) => updateName(e.target.value)}
          placeholder="Enter your name"
          style={{
            padding: '8px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            marginRight: '10px',
            width: '200px'
          }}
        />
      </div>
      <div style={{
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#28a745',
        margin: '10px 0'
      }}>
        Hello, {name}! 👋
      </div>
    </div>
  );
};
