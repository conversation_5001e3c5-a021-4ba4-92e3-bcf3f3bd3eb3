/* App Layout */
#root {
  padding: 1rem;
  width: 100%;
}

.app-container {
  min-height: 100vh;
  width: 100%;
}

/* Header Styles */
.app-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.app-title {
  margin: 0 0 16px 0;
  font-size: 48px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-description {
  font-size: 20px;
  opacity: 0.9;
  line-height: 1.5;
  max-width: 600px;
  margin: 0 auto 20px auto;
}

.header-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.clear-button {
  padding: 10px 20px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Main Layout */
.main-layout {
  display: flex;
  gap: 24px;
  min-height: 1000px;
}

/* Widget Catalog */
.catalog-container {
  width: 350px;
  flex-shrink: 0;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.catalog-header {
  margin-bottom: 16px;
}

.catalog-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.catalog-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.4;
}

/* Layout Area */
.layout-area {
  flex: 1;
  border: 2px dashed #3498db;
  border-radius: 16px;
  min-height: 900px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.layout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 800px;
  padding: 20px;
  background-color: #fafbfc;
  overflow: hidden;
}

.layout-container {
  flex: 1;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
  font-size: 20px;
  opacity: 0.7;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

/* Grid layout */
.react-grid-item {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 2px solid #e9ecef;
  background-color: #fff;
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;
  cursor: move;
}

.react-grid-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  border-color: #3498db;
}

.react-grid-item.react-draggable-dragging {
  transform: rotate(3deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
  border-color: #2980b9;
  transition: none;
  opacity: 0.8;
}

.react-grid-item.react-grid-placeholder {
  background: linear-gradient(
    135deg,
    rgba(52, 152, 219, 0.1) 0%,
    rgba(52, 152, 219, 0.2) 100%
  );
  border: 3px solid #3498db;
  border-radius: 16px;
  animation: pulse-placeholder 1.5s ease-in-out infinite;
}

@keyframes pulse-placeholder {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* Widget Container */
.grid-widget-container {
  position: relative;
  height: 100%;
}

.grid-widget-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.widget-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1000;
}

.widget-action-btn {
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.widget-action-btn:hover {
  background: rgba(220, 53, 69, 1);
  transform: scale(1.1);
}

.widget-body {
  flex: 1;
  overflow: hidden;
  pointer-events: auto;
}

/* Widget Catalog Cards */
.widgets-catalog {
  padding: 16px;
}

.widgets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.widget-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin: 8px;
  cursor: pointer;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
  user-select: none;
}

.widget-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.widget-card.draggable {
  cursor: grab;
}

.widget-card h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.widget-card p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.widget-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.widget-tag {
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: #666;
}

.no-widgets {
  grid-column: 1 / -1;
  text-align: center;
  color: #666;
}

/* React Grid Layout Resize Handle */
.react-resizable-handle {
  position: absolute !important;
  width: 20px !important;
  height: 20px !important;
  bottom: 0 !important;
  right: 0 !important;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
  border-radius: 16px 0 14px 0 !important;
  cursor: se-resize !important;
  z-index: 1000 !important;
  opacity: 0.8 !important;
  transition: all 0.2s ease !important;
}

.react-resizable-handle:hover {
  opacity: 1 !important;
  transform: scale(1.1) !important;
}

.react-resizable-handle::after {
  content: "⋱" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 12px !important;
  font-weight: bold !important;
}
